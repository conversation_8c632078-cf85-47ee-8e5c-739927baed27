package com.unimas.asn.client;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import com.unimas.asn.codec.OssCoerAdapter;
import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.util.concurrent.Executors;

/**
 * 告警上报测试服务器
 * 用于接收和处理AlarmMonitoringService发送的告警报告
 */
public class AlarmReportServer {
    private static final Logger logger = LoggerFactory.getLogger(AlarmReportServer.class);
    
    private HttpServer server;
    private final int port;
    private final String path;
    
    /**
     * 创建告警上报服务器
     * @param port 监听端口
     * @param path 接收路径
     */
    public AlarmReportServer(int port, String path) {
        this.port = port;
        this.path = path;
    }
    
    /**
     * 启动服务器
     */
    public void start() throws IOException {
        server = HttpServer.create(new InetSocketAddress(port), 0);
        server.createContext(path, new AlarmHandler());
        server.setExecutor(Executors.newFixedThreadPool(4));
        server.start();
        
        logger.info("Alarm report server started on port {} with path {}", port, path);
        logger.info("Server URL: http://localhost:{}{}", port, path);
    }
    
    /**
     * 停止服务器
     */
    public void stop() {
        if (server != null) {
            server.stop(0);
            logger.info("Alarm report server stopped");
        }
    }
    
    /**
     * 告警处理器
     */
    private static class AlarmHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            logger.info("Received alarm report request from: {}", exchange.getRemoteAddress());
            
            try {
                // 只处理POST请求
                if (!"POST".equals(exchange.getRequestMethod())) {
                    sendErrorResponse(exchange, 405, "Method Not Allowed");
                    return;
                }
                
                // 读取请求体
                byte[] requestBody = readRequestBody(exchange);
                logger.info("Received request body: {}", Hex.toHexString(requestBody));
                
                // 解码请求
                MessageRequestFrame frame = OssCoerAdapter.decode(requestBody, MessageRequestFrame.class);
                logger.info("Decoded message frame: {}", frame);
                
                // 处理告警报告
                if (frame.getContent() != null && frame.getContent().hasAlarmReportRequest()) {
                    AlarmReportRequest alarmRequest = frame.getContent().getAlarmReportRequest();
                    processAlarmReport(alarmRequest);
                    
                    // 创建响应
                    MessageResponseFrame responseFrame = createSuccessResponse(alarmRequest);
                    byte[] responseData = OssCoerAdapter.encode(responseFrame);
                    
                    // 发送响应
                    sendSuccessResponse(exchange, responseData);
                    logger.info("Sent response: {}", Hex.toHexString(responseData));
                } else {
                    logger.warn("Invalid request: no alarm report found");
                    sendErrorResponse(exchange, 400, "Invalid request format");
                }
                
            } catch (Exception e) {
                logger.error("Error processing alarm report", e);
                sendErrorResponse(exchange, 500, "Internal Server Error");
            }
        }
        
        /**
         * 处理告警报告
         */
        private void processAlarmReport(AlarmReportRequest request) {
            logger.info("=== Processing Alarm Report ===");
            logger.info("Message Type: {}", request.getMessageType());
            logger.info("Service ID: {}", request.getServiceId());
            
            if (request.getAlarms() != null) {
                logger.info("Number of alarms: {}", request.getAlarms().size());
                
                for (int i = 0; i < request.getAlarms().size(); i++) {
                    AlarmItem alarm = request.getAlarms().get(i);
                    logger.info("--- Alarm {} ---", i + 1);
                    logger.info("  Alarm Type: {}", alarm.getAlarmType());
                    logger.info("  Alarm Code: {}", alarm.getAlarmCode());
                    logger.info("  Alarm Count: {}", alarm.getAlarmCount());
                    if (alarm.hasAlarmDesc()) {
                        logger.info("  Alarm Description: {}", alarm.getAlarmDesc());
                    }
                }
            } else {
                logger.info("No alarms in the request");
            }
            logger.info("=== End Alarm Report ===");
        }
        
        /**
         * 创建成功响应
         */
        private MessageResponseFrame createSuccessResponse(AlarmReportRequest request) {
            MessageResponseFrame responseFrame = new MessageResponseFrame();
            responseFrame.setVersion(new Uint8(1));
            responseFrame.setContent(new MessageResponseFrame.Content());
            
            AlarmReportResponse response = new AlarmReportResponse();
            response.setMessageType(ContentMessageType.reportAlarm);
            response.setServiceId(request.getServiceId());
            
            responseFrame.getContent().setAlarmReportResponse(response);
            return responseFrame;
        }
        
        /**
         * 读取请求体
         */
        private byte[] readRequestBody(HttpExchange exchange) throws IOException {
            try (InputStream is = exchange.getRequestBody()) {
                // JDK 8兼容的方式读取所有字节
                byte[] buffer = new byte[8192];
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
                return baos.toByteArray();
            }
        }
        
        /**
         * 发送成功响应
         */
        private void sendSuccessResponse(HttpExchange exchange, byte[] responseData) throws IOException {
            exchange.getResponseHeaders().set("Content-Type", "application/octet-stream");
            exchange.sendResponseHeaders(200, responseData.length);
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(responseData);
                os.flush();
            }
        }
        
        /**
         * 发送错误响应
         */
        private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
            byte[] response = message.getBytes();
            exchange.getResponseHeaders().set("Content-Type", "text/plain");
            exchange.sendResponseHeaders(statusCode, response.length);
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response);
                os.flush();
            }
        }
    }
    
    /**
     * 主方法，用于启动测试服务器
     */
    public static void main(String[] args) {
        // 默认配置
        int port = 8080;
        String path = "/alarm";
        
        // 解析命令行参数
        if (args.length >= 1) {
            try {
                port = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                System.err.println("Invalid port number: " + args[0]);
                System.exit(1);
            }
        }
        
        if (args.length >= 2) {
            path = args[1];
        }
        
        AlarmReportServer server = new AlarmReportServer(port, path);
        
        try {
            server.start();
            
            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("Shutting down alarm report server...");
                server.stop();
            }));
            
            // 保持服务器运行
            logger.info("Press Ctrl+C to stop the server");
            Thread.currentThread().join();
            
        } catch (Exception e) {
            logger.error("Failed to start alarm report server", e);
            System.exit(1);
        }
    }
}
